package com.xiang.proxy.server.inbound.impl.multiplex;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;

/**
 * 多路复用会话监控器
 * 提供详细的会话状态监控和数据流向跟踪
 */
public class MultiplexSessionMonitor {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexSessionMonitor.class);
    
    // 单例实例
    private static final MultiplexSessionMonitor INSTANCE = new MultiplexSessionMonitor();
    
    // 会话统计信息
    private final AtomicLong totalSessionsCreated = new AtomicLong(0);
    private final AtomicLong totalSessionsClosed = new AtomicLong(0);
    private final AtomicLong totalDataPacketsReceived = new AtomicLong(0);
    private final AtomicLong totalDataPacketsForwarded = new AtomicLong(0);
    private final AtomicLong totalDataPacketsDropped = new AtomicLong(0);
    private final AtomicInteger activeSessionsCount = new AtomicInteger(0);
    
    // 会话详细信息
    private final ConcurrentHashMap<Integer, SessionInfo> activeSessions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, ConnectionInfo> activeConnections = new ConcurrentHashMap<>();
    
    private MultiplexSessionMonitor() {}
    
    public static MultiplexSessionMonitor getInstance() {
        return INSTANCE;
    }
    
    /**
     * 记录会话创建
     */
    public void recordSessionCreated(int sessionId, String connectionId, String hostKey, long clientConnectionId) {
        totalSessionsCreated.incrementAndGet();
        activeSessionsCount.incrementAndGet();
        
        SessionInfo sessionInfo = new SessionInfo(sessionId, connectionId, hostKey, clientConnectionId, System.currentTimeMillis());
        activeSessions.put(sessionId, sessionInfo);
        
        logger.debug("会话创建: sessionId={}, connectionId={}, hostKey={}, clientConnectionId={}, 活跃会话数={}",
                    sessionId, connectionId, hostKey, clientConnectionId, activeSessionsCount.get());
    }
    
    /**
     * 记录会话关闭
     */
    public void recordSessionClosed(int sessionId, String reason) {
        totalSessionsClosed.incrementAndGet();
        activeSessionsCount.decrementAndGet();
        
        SessionInfo sessionInfo = activeSessions.remove(sessionId);
        if (sessionInfo != null) {
            sessionInfo.closeTime = System.currentTimeMillis();
            sessionInfo.closeReason = reason;
            
            long sessionDuration = sessionInfo.closeTime - sessionInfo.createTime;
            logger.debug("会话关闭: sessionId={}, reason={}, 持续时间={}ms, 数据包: 接收={}, 转发={}, 丢弃={}, 活跃会话数={}",
                        sessionId, reason, sessionDuration, sessionInfo.packetsReceived, 
                        sessionInfo.packetsForwarded, sessionInfo.packetsDropped, activeSessionsCount.get());
        } else {
            logger.warn("尝试关闭不存在的会话: sessionId={}, reason={}", sessionId, reason);
        }
    }
    
    /**
     * 记录数据包接收
     */
    public void recordDataPacketReceived(int sessionId, int bytes) {
        totalDataPacketsReceived.incrementAndGet();
        
        SessionInfo sessionInfo = activeSessions.get(sessionId);
        if (sessionInfo != null) {
            sessionInfo.packetsReceived++;
            sessionInfo.bytesReceived += bytes;
            sessionInfo.lastActivityTime = System.currentTimeMillis();
            
            if (logger.isTraceEnabled()) {
                logger.trace("数据包接收: sessionId={}, bytes={}, 累计接收={}", 
                           sessionId, bytes, sessionInfo.packetsReceived);
            }
        } else {
            logger.warn("收到未知会话的数据包: sessionId={}, bytes={}", sessionId, bytes);
        }
    }
    
    /**
     * 记录数据包转发
     */
    public void recordDataPacketForwarded(int sessionId, int bytes) {
        totalDataPacketsForwarded.incrementAndGet();
        
        SessionInfo sessionInfo = activeSessions.get(sessionId);
        if (sessionInfo != null) {
            sessionInfo.packetsForwarded++;
            sessionInfo.bytesForwarded += bytes;
            
            if (logger.isTraceEnabled()) {
                logger.trace("数据包转发: sessionId={}, bytes={}, 累计转发={}", 
                           sessionId, bytes, sessionInfo.packetsForwarded);
            }
        }
    }
    
    /**
     * 记录数据包丢弃
     */
    public void recordDataPacketDropped(int sessionId, int bytes, String reason) {
        totalDataPacketsDropped.incrementAndGet();
        
        SessionInfo sessionInfo = activeSessions.get(sessionId);
        if (sessionInfo != null) {
            sessionInfo.packetsDropped++;
            sessionInfo.bytesDropped += bytes;
            
            logger.debug("数据包丢弃: sessionId={}, bytes={}, reason={}, 累计丢弃={}", 
                        sessionId, bytes, reason, sessionInfo.packetsDropped);
        }
    }
    
    /**
     * 记录连接信息
     */
    public void recordConnectionInfo(String connectionId, String targetHost, int targetPort, String status) {
        ConnectionInfo connectionInfo = activeConnections.computeIfAbsent(connectionId, 
            k -> new ConnectionInfo(connectionId, targetHost, targetPort, System.currentTimeMillis()));
        
        connectionInfo.status = status;
        connectionInfo.lastUpdateTime = System.currentTimeMillis();
        
        logger.debug("连接状态更新: connectionId={}, target={}:{}, status={}", 
                    connectionId, targetHost, targetPort, status);
    }
    
    /**
     * 移除连接信息
     */
    public void removeConnectionInfo(String connectionId) {
        ConnectionInfo removed = activeConnections.remove(connectionId);
        if (removed != null) {
            logger.debug("连接信息移除: connectionId={}, target={}:{}", 
                        connectionId, removed.targetHost, removed.targetPort);
        }
    }
    
    /**
     * 获取监控统计信息
     */
    public MonitorStats getStats() {
        return new MonitorStats(
            totalSessionsCreated.get(),
            totalSessionsClosed.get(),
            activeSessionsCount.get(),
            totalDataPacketsReceived.get(),
            totalDataPacketsForwarded.get(),
            totalDataPacketsDropped.get(),
            activeSessions.size(),
            activeConnections.size()
        );
    }
    
    /**
     * 打印详细统计信息
     */
    public void printDetailedStats() {
        MonitorStats stats = getStats();
        logger.info("=== 多路复用会话监控统计 ===");
        logger.info("会话统计: 创建={}, 关闭={}, 活跃={}", 
                   stats.totalSessionsCreated, stats.totalSessionsClosed, stats.activeSessionsCount);
        logger.info("数据包统计: 接收={}, 转发={}, 丢弃={}, 转发率={:.2f}%", 
                   stats.totalDataPacketsReceived, stats.totalDataPacketsForwarded, stats.totalDataPacketsDropped,
                   stats.totalDataPacketsReceived > 0 ? (stats.totalDataPacketsForwarded * 100.0 / stats.totalDataPacketsReceived) : 0.0);
        logger.info("活跃连接数: {}", stats.activeConnectionsCount);
        
        // 打印活跃会话详情
        if (!activeSessions.isEmpty()) {
            logger.info("活跃会话详情:");
            activeSessions.forEach((sessionId, sessionInfo) -> {
                long duration = System.currentTimeMillis() - sessionInfo.createTime;
                logger.info("  会话{}: 连接={}, 持续时间={}ms, 数据包: 接收={}, 转发={}, 丢弃={}",
                           sessionId, sessionInfo.connectionId, duration,
                           sessionInfo.packetsReceived, sessionInfo.packetsForwarded, sessionInfo.packetsDropped);
            });
        }
    }
    
    /**
     * 会话信息
     */
    private static class SessionInfo {
        final int sessionId;
        final String connectionId;
        final String hostKey;
        final long clientConnectionId;
        final long createTime;
        long closeTime;
        String closeReason;
        long lastActivityTime;
        
        long packetsReceived = 0;
        long packetsForwarded = 0;
        long packetsDropped = 0;
        long bytesReceived = 0;
        long bytesForwarded = 0;
        long bytesDropped = 0;
        
        SessionInfo(int sessionId, String connectionId, String hostKey, long clientConnectionId, long createTime) {
            this.sessionId = sessionId;
            this.connectionId = connectionId;
            this.hostKey = hostKey;
            this.clientConnectionId = clientConnectionId;
            this.createTime = createTime;
            this.lastActivityTime = createTime;
        }
    }
    
    /**
     * 连接信息
     */
    private static class ConnectionInfo {
        final String connectionId;
        final String targetHost;
        final int targetPort;
        final long createTime;
        String status;
        long lastUpdateTime;
        
        ConnectionInfo(String connectionId, String targetHost, int targetPort, long createTime) {
            this.connectionId = connectionId;
            this.targetHost = targetHost;
            this.targetPort = targetPort;
            this.createTime = createTime;
            this.lastUpdateTime = createTime;
        }
    }
    
    /**
     * 监控统计信息
     */
    public static class MonitorStats {
        public final long totalSessionsCreated;
        public final long totalSessionsClosed;
        public final int activeSessionsCount;
        public final long totalDataPacketsReceived;
        public final long totalDataPacketsForwarded;
        public final long totalDataPacketsDropped;
        public final int activeSessionsMapSize;
        public final int activeConnectionsCount;
        
        MonitorStats(long totalSessionsCreated, long totalSessionsClosed, int activeSessionsCount,
                    long totalDataPacketsReceived, long totalDataPacketsForwarded, long totalDataPacketsDropped,
                    int activeSessionsMapSize, int activeConnectionsCount) {
            this.totalSessionsCreated = totalSessionsCreated;
            this.totalSessionsClosed = totalSessionsClosed;
            this.activeSessionsCount = activeSessionsCount;
            this.totalDataPacketsReceived = totalDataPacketsReceived;
            this.totalDataPacketsForwarded = totalDataPacketsForwarded;
            this.totalDataPacketsDropped = totalDataPacketsDropped;
            this.activeSessionsMapSize = activeSessionsMapSize;
            this.activeConnectionsCount = activeConnectionsCount;
        }
    }
}
