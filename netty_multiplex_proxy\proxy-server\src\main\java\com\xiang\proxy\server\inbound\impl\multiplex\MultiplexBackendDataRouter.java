package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.protocol.MultiplexProtocol;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

/**
 * 多路复用后端数据路由器
 * 支持动态会话路由，解决队列模式下的数据路由问题
 */
public class MultiplexBackendDataRouter extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexBackendDataRouter.class);
    
    // 全局会话路由表：连接ID -> 会话路由信息
    private static final ConcurrentHashMap<String, SessionRouteInfo> globalSessionRoutes = new ConcurrentHashMap<>();
    
    private final String connectionId;
    private volatile boolean errorHandled = false;
    
    // 数据包统计
    private volatile long packetsReceived = 0;
    private volatile long packetsForwarded = 0;
    private volatile long packetsDropped = 0;
    
    public MultiplexBackendDataRouter(String connectionId) {
        this.connectionId = connectionId;
    }
    
    /**
     * 注册会话路由信息
     */
    public static void registerSessionRoute(String connectionId, int sessionId, Channel clientChannel, 
                                          BiConsumer<Integer, String> errorHandler) {
        SessionRouteInfo routeInfo = new SessionRouteInfo(sessionId, clientChannel, errorHandler);
        globalSessionRoutes.put(connectionId, routeInfo);
        logger.debug("注册会话路由: connectionId={}, sessionId={}", connectionId, sessionId);
    }
    
    /**
     * 注销会话路由信息
     */
    public static void unregisterSessionRoute(String connectionId) {
        SessionRouteInfo removed = globalSessionRoutes.remove(connectionId);
        if (removed != null) {
            logger.debug("注销会话路由: connectionId={}, sessionId={}", connectionId, removed.sessionId);
        }
    }
    
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (!(msg instanceof ByteBuf)) {
            logger.warn("收到非ByteBuf消息: connectionId={}, msgType={}", connectionId, msg.getClass());
            return;
        }
        
        ByteBuf data = (ByteBuf) msg;
        boolean dataProcessed = false;
        long startTime = System.currentTimeMillis();
        
        try {
            // 统计接收到的数据包
            packetsReceived++;
            
            // 获取会话路由信息
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo == null) {
                logger.warn("未找到会话路由信息: connectionId={}, 丢弃数据", connectionId);
                packetsDropped++;
                return;
            }
            
            // 检查客户端连接状态
            if (routeInfo.clientChannel == null || !routeInfo.clientChannel.isActive()) {
                logger.warn("客户端连接不可用，丢弃后端数据: connectionId={}, sessionId={}, bytes={}", 
                           connectionId, routeInfo.sessionId, data.readableBytes());
                packetsDropped++;
                routeInfo.errorHandler.accept(routeInfo.sessionId, "客户端连接不可用");
                return;
            }
            
            // 检查数据是否有效
            if (!data.isReadable()) {
                logger.debug("收到空数据: connectionId={}, sessionId={}", connectionId, routeInfo.sessionId);
                packetsDropped++;
                return;
            }
            
            // 零拷贝优化：创建多路复用数据包
            MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createZeroCopyDataPacket(
                routeInfo.sessionId, data, true);
            ByteBuf packetBuffer = dataPacket.encode();
            
            // 标记数据已被处理
            dataProcessed = true;
            
            // 转发数据到客户端
            routeInfo.clientChannel.writeAndFlush(packetBuffer).addListener(future -> {
                long processingTime = System.currentTimeMillis() - startTime;
                if (!future.isSuccess()) {
                    packetsDropped++;
                    // 记录数据包丢弃到监控器
                    MultiplexSessionMonitor.getInstance().recordDataPacketDropped(
                        routeInfo.sessionId, data.readableBytes(), "转发失败: " + future.cause().getMessage());

                    logger.error("转发后端数据到客户端失败: connectionId={}, sessionId={}, 处理时间={}ms",
                               connectionId, routeInfo.sessionId, processingTime, future.cause());
                    routeInfo.errorHandler.accept(routeInfo.sessionId, "转发数据失败: " + future.cause().getMessage());
                } else {
                    packetsForwarded++;

                    // 记录数据包转发到监控器
                    MultiplexSessionMonitor.getInstance().recordDataPacketForwarded(routeInfo.sessionId, data.readableBytes());

                    if (logger.isDebugEnabled()) {
                        logger.debug("转发后端数据到客户端成功: connectionId={}, sessionId={}, bytes={}, 处理时间={}ms, 转发率={:.2f}%",
                                   connectionId, routeInfo.sessionId, data.readableBytes(), processingTime,
                                   packetsReceived > 0 ? (packetsForwarded * 100.0 / packetsReceived) : 0.0);
                    }

                    // 性能监控：记录处理延迟
                    if (processingTime > 100) {
                        logger.warn("数据转发延迟较高: connectionId={}, sessionId={}, 处理时间={}ms",
                                   connectionId, routeInfo.sessionId, processingTime);
                    }
                }
            });
            
        } catch (Exception e) {
            logger.error("处理后端数据时发生异常: connectionId={}", connectionId, e);
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo != null) {
                routeInfo.errorHandler.accept(routeInfo.sessionId, "数据处理异常: " + e.getMessage());
            }
        } finally {
            // 只有在数据没有被处理时才释放，避免双重释放
            if (!dataProcessed) {
                data.release();
            }
        }
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (!errorHandled) {
            errorHandled = true;
            logger.debug("后端连接断开: connectionId={}, remote={}, 统计: 接收={}, 转发={}, 丢弃={}",
                        connectionId, ctx.channel().remoteAddress(), packetsReceived, packetsForwarded, packetsDropped);
            
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo != null) {
                routeInfo.errorHandler.accept(routeInfo.sessionId, "后端连接断开");
            }
        }
        
        // 清理路由信息
        unregisterSessionRoute(connectionId);
        super.channelInactive(ctx);
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (!errorHandled) {
            errorHandled = true;
            
            // 根据异常类型提供更具体的错误信息
            String errorMessage;
            if (cause instanceof java.net.ConnectException) {
                errorMessage = "后端连接失败";
            } else if (cause instanceof java.io.IOException && cause.getMessage().contains("Connection reset")) {
                errorMessage = "后端连接异常: Connection reset";
            } else if (cause instanceof java.io.IOException) {
                errorMessage = "后端连接IO异常";
            } else {
                errorMessage = "后端连接异常: " + cause.getMessage();
            }
            
            logger.warn("后端连接异常: connectionId={}, error={}", connectionId, errorMessage);
            
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo != null) {
                routeInfo.errorHandler.accept(routeInfo.sessionId, errorMessage);
            }
        }
        
        // 清理路由信息
        unregisterSessionRoute(connectionId);
        super.exceptionCaught(ctx, cause);
    }
    
    /**
     * 会话路由信息
     */
    private static class SessionRouteInfo {
        final int sessionId;
        final Channel clientChannel;
        final BiConsumer<Integer, String> errorHandler;
        
        SessionRouteInfo(int sessionId, Channel clientChannel, BiConsumer<Integer, String> errorHandler) {
            this.sessionId = sessionId;
            this.clientChannel = clientChannel;
            this.errorHandler = errorHandler;
        }
    }
    
    /**
     * 获取统计信息
     */
    public String getPacketStats() {
        return String.format("接收=%d, 转发=%d, 丢弃=%d", packetsReceived, packetsForwarded, packetsDropped);
    }
}
